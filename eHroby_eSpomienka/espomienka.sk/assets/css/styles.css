/* <PERSON> Memorial Website - Optimized Styles */

/* ===== GOOGLE FONTS ===== */
@import url('https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Lora:wght@400;500;600&family=Nunito:wght@300;400;500;600;700&display=swap');

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Unified Color System - White, Black, Gold */
  --gold-50: #fffdf7;
  --gold-100: #fffaeb;
  --gold-200: #fef3c7;
  --gold-300: #fde68a;
  --gold-400: #fcd34d;
  --gold-500: #f59e0b;
  --gold-600: #d97706;
  --gold-700: #b45309;
  --gold-800: #92400e;
  --gold-900: #78350f;
  --black-50: #fafafa;
  --black-100: #f5f5f5;
  --black-200: #e5e5e5;
  --black-300: #d4d4d4;
  --black-400: #a3a3a3;
  --black-500: #737373;
  --black-600: #525252;
  --black-700: #404040;
  --black-800: #262626;
  --black-900: #171717;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: var(--black-100);
  --bg-accent: var(--black-50);
  --bg-soft: var(--black-50);

  /* Text Colors */
  --text-primary: var(--black-900);
  --text-secondary: var(--black-700);
  --text-muted: var(--black-500);

  /* Brand Colors */
  --brand-primary: var(--gold-600);
  --brand-secondary: var(--gold-500);
  --brand-accent: var(--gold-400);

  /* Legacy Gold Colors (for compatibility) */
  --gold-primary: var(--gold-600);
  --gold-light: var(--gold-400);
  --gold-dark: var(--gold-700);

  /* Gradients */
  --gradient-gold: linear-gradient(135deg, var(--gold-600) 0%, var(--gold-700) 100%);
  --gradient-hero: linear-gradient(135deg, rgba(245, 158, 11, 0.05) 0%, rgba(217, 119, 6, 0.08) 100%);

  /* Border & Shadow */
  --border-light: var(--black-200);
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.05);
  --shadow-md: 0 4px 6px rgba(0,0,0,0.07);
  --shadow-lg: 0 10px 15px rgba(0,0,0,0.08);

  /* Typography */
  --font-primary: 'Lora', serif;
  --font-body: 'Inter', sans-serif;
  --font-handwritten: 'Dancing Script', cursive;
  --font-blog: 'Nunito', sans-serif;

  /* Spacing */
  --section-padding: 5rem 0;
  --section-padding-mobile: 4rem 0;
  --container-max-width: 1200px;
  --container-padding: 0 1.5rem;
  --grid-gap: 2rem;
  --card-padding: 2rem;
  --card-radius: 0.75rem;

  /* Responsive Breakpoints */
  --mobile: 767px;
  --tablet: 768px;
  --desktop: 1024px;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-body);
  font-size: 1rem;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-primary);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: var(--text-primary);
}

h1 {
  font-size: 3rem;
  font-weight: 300;
}

h2 {
  font-size: 2.25rem;
  font-weight: 600;
  margin-bottom: 4rem;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
}

h4 {
  font-size: 1.25rem;
  font-weight: 500;
}

/* Mobile Typography */
@media (max-width: 767px) {
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.75rem;
  }
}

/* Handwritten Styles */
.handwritten-accent {
  font-family: var(--font-handwritten);
  font-weight: 600;
  font-size: 1.2em;
  color: var(--gold-primary);
}

.handwritten-quote {
  font-family: var(--font-handwritten);
  font-weight: 500;
  font-size: 1.5rem;
  color: var(--gold-primary);
  text-align: center;
  margin: 2rem 0;
}

.handwritten-signature {
  font-family: var(--font-handwritten);
  font-weight: 500;
  font-size: 1.1rem;
  color: var(--gold-primary);
}

/* Blog Typography */
.blog-heading {
  font-family: var(--font-blog);
  font-weight: 400;
}

/* Body Text Variants */
.body-large {
  font-size: 1.25rem;
}

.body-small {
  font-size: 0.95rem;
}

/* Menu Typography */
.menu-item {
  font-family: var(--font-primary);
  font-weight: 500;
  font-size: 0.95rem;
}

/* ===== LAYOUT COMPONENTS ===== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: var(--container-padding);
}

.section {
  padding: var(--section-padding);
}

@media (max-width: 767px) {
  .section {
    padding: var(--section-padding-mobile);
  }
}

/* Grid System */
.grid {
  display: grid;
  gap: var(--grid-gap);
}

@media (max-width: 767px) {
  .grid {
    gap: var(--grid-gap-mobile);
  }
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 1023px) {
  .grid-3,
  .grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 767px) {
  .grid-2,
  .grid-3,
  .grid-4 {
    grid-template-columns: 1fr;
  }
}

/* ===== BUTTONS ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  border: none;
  border-radius: 0.5rem;
  font-family: var(--font-primary);
  font-weight: 500;
  font-size: 1rem;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-md);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-primary {
  background: var(--brand-primary);
  color: white;
}

.btn-primary:hover {
  background: var(--gold-700);
}

.btn-secondary {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 2px solid var(--brand-secondary);
}

.btn-secondary:hover {
  background: var(--brand-secondary);
  color: white;
}

.btn-outline {
  background: transparent;
  color: var(--brand-primary);
  border: 2px solid var(--brand-primary);
}

.btn-outline:hover {
  background: var(--brand-primary);
  color: white;
}

.btn-large {
  padding: 1.25rem 2.5rem;
  font-size: 1.1rem;
}

/* ===== CARDS ===== */
.card {
  background: var(--bg-primary);
  border-radius: 1rem;
  padding: var(--card-padding);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

@media (max-width: 767px) {
  .card {
    padding: var(--card-padding-mobile);
  }
}

/* ===== UTILITY CLASSES ===== */
.text-center {
  text-align: center;
}

.text-gold {
  color: var(--gold-primary);
}

.bg-secondary {
  background-color: var(--bg-secondary);
}

.bg-accent {
  background-color: var(--bg-accent);
}

.bg-soft {
  background-color: var(--bg-soft);
}

.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }

/* ===== RESPONSIVE IMAGES ===== */
img {
  max-width: 100%;
  height: auto;
}

/* ===== ACCESSIBILITY ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--gold-primary);
  outline-offset: 2px;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* ===== LOADING STATES ===== */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* ===== HEADER & NAVIGATION ===== */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-light);
  transition: all 0.3s ease;
}

.header.scrolled {
  box-shadow: var(--shadow-md);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 0;
}

.logo {
  font-family: var(--font-handwritten);
  font-size: 2rem;
  font-weight: 600;
  color: var(--gold-primary);
  text-decoration: none;
}

.nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
  margin: 0;
  padding: 0;
}

.nav-link {
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: var(--gold-primary);
}

.nav-cta {
  margin-left: 1rem;
}

.phone-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-primary);
  text-decoration: none;
  font-weight: 500;
}

.phone-link:hover {
  color: var(--gold-primary);
}

/* Mobile Menu */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-primary);
}

@media (max-width: 1023px) {
  .nav-menu {
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    flex-direction: column;
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .nav-menu.open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .phone-link {
    display: none;
  }
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: var(--gradient-hero);
  position: relative;
  overflow: hidden;
  padding-top: 80px;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-quote {
  margin: 3rem 0;
  font-style: italic;
}

.trust-badges {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.trust-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.trust-badge::before {
  content: "✓";
  color: var(--gold-primary);
  font-weight: bold;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-top: 4rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-light);
}

.hero-stat {
  text-align: center;
}

.hero-stat-number {
  font-size: 2rem;
  font-weight: 600;
  color: var(--gold-primary);
  display: block;
}

.hero-stat-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

@media (max-width: 767px) {
  .hero-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .trust-badges {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }
}

/* ===== PRICING SECTION ===== */
.pricing {
  background: var(--bg-primary);
  padding: var(--section-padding);
}

.pricing-intro {
  text-align: center;
  margin-bottom: 4rem;
}

.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.pricing-card {
  background: var(--bg-primary);
  border: 2px solid var(--border-light);
  border-radius: var(--card-radius);
  padding: var(--card-padding);
  position: relative;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.pricing-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.pricing-popular {
  border-color: var(--brand-primary);
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.pricing-popular:hover {
  transform: scale(1.05) translateY(-5px);
}

.pricing-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--brand-primary);
  color: white;
  padding: 0.5rem 1.5rem;
  border-radius: 2rem;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pricing-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid var(--border-light);
}

.pricing-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.pricing-price {
  margin-bottom: 0.5rem;
}

.price-amount {
  font-size: 3rem;
  font-weight: 700;
  color: var(--brand-primary);
  font-family: var(--font-primary);
}

.price-period {
  font-size: 1rem;
  color: var(--text-secondary);
  margin-left: 0.5rem;
}

.pricing-subtitle {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin: 0;
}

.pricing-features {
  margin-bottom: 2rem;
}

.feature-group {
  margin-bottom: 1.5rem;
}

.feature-group h4 {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features-list li {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.feature-included::before {
  content: "✓";
  color: var(--brand-primary);
  font-weight: bold;
  font-size: 1rem;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.feature-excluded::before {
  content: "✗";
  color: var(--text-muted);
  font-weight: bold;
  font-size: 1rem;
  flex-shrink: 0;
  margin-top: 0.1rem;
}

.feature-excluded {
  color: var(--text-muted);
  opacity: 0.7;
}

.pricing-footer {
  text-align: center;
}

/* Pricing Card Variants */
.pricing-basic {
  border-color: var(--black-300);
}

.pricing-standard {
  border-color: var(--brand-primary);
}

.pricing-premium {
  border-color: var(--brand-secondary);
}

/* Pricing Guarantee Section */
.pricing-guarantee {
  background: var(--bg-secondary);
  border-radius: var(--card-radius);
  padding: 3rem 2rem;
  text-align: center;
}

.guarantee-content h3 {
  margin-bottom: 2rem;
}

.guarantee-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.guarantee-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  text-align: left;
}

.guarantee-icon {
  width: 40px;
  height: 40px;
  fill: var(--brand-primary);
  flex-shrink: 0;
}

.guarantee-item h4 {
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.guarantee-item p {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
}

.guarantee-cta {
  margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .pricing-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1023px) {
  .pricing-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .pricing-popular {
    transform: none;
  }

  .pricing-popular:hover {
    transform: translateY(-5px);
  }

  .guarantee-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 767px) {
  .pricing {
    padding: var(--section-padding-mobile);
  }

  .pricing-intro {
    margin-bottom: 3rem;
  }

  .pricing-card {
    padding: 1.5rem;
  }

  .price-amount {
    font-size: 2.5rem;
  }

  .pricing-guarantee {
    padding: 2rem 1rem;
  }

  .guarantee-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .guarantee-item {
    text-align: center;
    flex-direction: column;
    align-items: center;
  }

  .features-list li {
    font-size: 0.85rem;
  }

  .pricing-title {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .pricing-card {
    padding: 1rem;
  }

  .price-amount {
    font-size: 2rem;
  }

  .pricing-badge {
    font-size: 0.7rem;
    padding: 0.4rem 1rem;
  }
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio {
  background: linear-gradient(135deg, var(--bg-soft) 0%, var(--bg-secondary) 100%);
  padding: var(--section-padding);
}

.portfolio-intro {
  text-align: center;
  margin-bottom: 4rem;
}

.portfolio-intro h2 {
  margin-bottom: 1rem;
}

.video-container {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.video-player {
  width: 100%;
  height: 450px;
  border-radius: 1.5rem;
  box-shadow: var(--shadow-lg);
  margin-bottom: 1.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-player:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.video-caption {
  color: var(--text-secondary);
  font-style: italic;
  margin-bottom: 2rem;
  font-size: 1rem;
}

/* Portfolio Grid */
.portfolio-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.portfolio-item {
  background: var(--bg-primary);
  border-radius: 1.5rem;
  padding: 2.5rem;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.portfolio-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
}

.portfolio-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

/* Memorial Website Showcase */
.memorial-website-showcase {
  border: 2px solid var(--brand-primary);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-accent) 100%);
}

.memorial-preview-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.memorial-preview-header h3 {
  font-size: 1.75rem;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.memorial-preview-image {
  position: relative;
  border-radius: 1rem;
  overflow: hidden;
  margin-bottom: 2rem;
  box-shadow: var(--shadow-md);
  transition: transform 0.3s ease;
}

.memorial-preview-image:hover {
  transform: scale(1.02);
}

.preview-img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.memorial-preview-image:hover .preview-img {
  transform: scale(1.05);
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.8));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.memorial-preview-image:hover .preview-overlay {
  opacity: 1;
}

.preview-text h4 {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.preview-text p {
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.preview-subtitle {
  font-size: 1rem;
  opacity: 0.95;
  font-style: italic;
  font-weight: 400;
}

.memorial-preview-description p {
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
  line-height: 1.7;
  font-size: 1rem;
}

.memorial-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 2rem;
}

.feature-tag {
  background: linear-gradient(135deg, var(--brand-primary) 0%, var(--brand-secondary) 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-tag:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Responsive Portfolio */
@media (max-width: 1024px) {
  .portfolio-grid {
    gap: 3rem;
  }

  .portfolio-item {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .portfolio {
    padding: var(--section-padding-mobile);
  }

  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: 2.5rem;
  }

  .portfolio-intro {
    margin-bottom: 3rem;
  }

  .memorial-features {
    justify-content: center;
    gap: 0.5rem;
  }

  .feature-tag {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .video-player {
    height: 300px;
  }

  .preview-img {
    height: 200px;
  }

  .preview-text h4 {
    font-size: 1.5rem;
  }

  .memorial-preview-header h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .portfolio-item {
    padding: 1.5rem;
  }

  .video-player {
    height: 250px;
    border-radius: 1rem;
  }

  .memorial-features {
    gap: 0.4rem;
  }

  .feature-tag {
    font-size: 0.75rem;
    padding: 0.3rem 0.6rem;
  }
}

/* ===== HOW IT WORKS SECTION ===== */
.how-it-works {
  background: var(--bg-secondary);
}

.how-it-works-intro {
  text-align: center;
  margin-bottom: 4rem;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-bottom: 3rem;
}

.process-step {
  text-align: center;
  position: relative;
}

.step-number {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gradient-gold);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 auto 1rem;
}

.step-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  opacity: 0.8;
}

.step-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.step-description {
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.timeline-note {
  text-align: center;
  margin-top: 2rem;
}

@media (max-width: 1023px) {
  .process-steps {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 767px) {
  .process-steps {
    grid-template-columns: 1fr;
  }
}

/* ===== WHY CHOOSE US SECTION ===== */
.why-choose-us {
  background: var(--bg-primary);
}

.why-choose-intro {
  text-align: center;
  margin-bottom: 4rem;
}

.credibility-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
  margin-bottom: 4rem;
}

.credibility-item {
  background: var(--bg-primary);
  padding: 2rem;
  border-radius: 0.75rem;
  box-shadow: var(--shadow-md);
  text-align: center;
}

.credibility-content h4 {
  margin-bottom: 0.5rem;
}

.credibility-content p {
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.trust-section {
  background: var(--bg-accent);
  border-radius: 1rem;
  padding: 3rem;
  text-align: center;
  margin-top: 3rem;
}

.trust-content {
  position: relative;
}

.trust-badges-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  margin-top: 2rem;
}

.trust-badge-item {
  text-align: center;
}

.trust-badge-value {
  font-size: 2rem;
  font-weight: 600;
  color: var(--gold-primary);
  display: block;
}

.trust-badge-label {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

@media (max-width: 1023px) {
  .credibility-grid {
    grid-template-columns: 1fr;
  }

  .trust-badges-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 767px) {
  .credibility-grid {
    grid-template-columns: 1fr;
  }

  .trust-badges-grid {
    grid-template-columns: 1fr;
  }
}

/* ===== FAQ SECTION ===== */
.faq {
  background: var(--bg-soft);
}

.faq-intro {
  text-align: center;
  margin-bottom: 3rem;
}

.faq-list {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  background: var(--bg-primary);
  border-radius: 1rem;
  margin-bottom: 1rem;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.faq-question {
  width: 100%;
  padding: 1.5rem;
  background: none;
  border: none;
  text-align: left;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.3s ease;
}

.faq-question:hover {
  background: var(--bg-secondary);
}

.faq-icon {
  font-size: 1.2rem;
  color: var(--gold-primary);
  transition: transform 0.3s ease;
}

.faq-item.open .faq-icon {
  transform: rotate(45deg);
}

.faq-answer {
  padding: 0 1.5rem;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item.open .faq-answer {
  padding: 0 1.5rem 1.5rem;
  max-height: 500px;
}

.faq-answer p {
  color: var(--text-secondary);
  line-height: 1.6;
}

.faq-cta {
  text-align: center;
  margin-top: 3rem;
}

/* ===== CONTACT SECTION ===== */
.contact {
  background: var(--bg-secondary);
  position: relative;
  overflow: hidden;
}

.contact-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  opacity: 0.05;
}

.contact-content {
  position: relative;
  z-index: 2;
}

.contact-intro {
  text-align: center;
  margin-bottom: 4rem;
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
}

.contact-info h3 {
  margin-bottom: 2rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
}

.contact-icon {
  width: 24px;
  height: 24px;
  fill: var(--gold-primary);
  flex-shrink: 0;
  margin-top: 0.25rem;
}

.contact-details h4 {
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.contact-details a {
  color: var(--gold-primary);
  text-decoration: none;
  font-weight: 500;
}

.contact-details a:hover {
  text-decoration: underline;
}

.contact-note {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.why-contact {
  background: var(--bg-primary);
  border-radius: 1rem;
  padding: 2rem;
  margin-top: 2rem;
}

.why-contact h4 {
  margin-bottom: 1rem;
}

.why-contact ul {
  list-style: none;
  padding: 0;
}

.why-contact li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.why-contact li::before {
  content: "✓";
  color: var(--gold-primary);
  font-weight: bold;
}

/* Contact Form */
.contact-form h3 {
  margin-bottom: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-light);
  border-radius: 0.5rem;
  font-family: var(--font-body);
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--gold-primary);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

.form-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.form-checkbox input {
  margin-top: 0.25rem;
}

.form-checkbox label {
  font-size: 0.9rem;
  line-height: 1.4;
}

.form-submit {
  width: 100%;
  margin-bottom: 1rem;
}

.form-notes {
  font-size: 0.9rem;
  color: var(--text-secondary);
  text-align: center;
}

.form-notes p {
  margin-bottom: 0.5rem;
}

@media (max-width: 1023px) {
  .contact-grid {
    grid-template-columns: 1fr;
    gap: 3rem;
  }
}

/* ===== FOOTER ===== */
.footer {
  background: var(--text-primary);
  color: white;
  padding: 4rem 0 2rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-brand h3 {
  color: var(--gold-primary);
  font-family: var(--font-handwritten);
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.footer-brand p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 1rem;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social a {
  color: var(--gold-primary);
  text-decoration: none;
  font-size: 1.2rem;
}

.footer-nav h4 {
  color: white;
  margin-bottom: 1rem;
}

.footer-nav ul {
  list-style: none;
  padding: 0;
}

.footer-nav li {
  margin-bottom: 0.5rem;
}

.footer-nav a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-nav a:hover {
  color: var(--gold-primary);
}

.footer-contact h4 {
  color: white;
  margin-bottom: 1rem;
}

.footer-contact p {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.5rem;
}

.footer-contact a {
  color: var(--gold-primary);
  text-decoration: none;
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  text-align: center;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-copyright {
  color: rgba(255, 255, 255, 0.6);
}

.footer-quote {
  color: var(--gold-primary);
  font-family: var(--font-handwritten);
  font-style: italic;
}

.footer-links {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: 0.9rem;
}

.footer-links a:hover {
  color: var(--gold-primary);
}

.footer-note {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.8rem;
  margin-top: 1rem;
}

@media (max-width: 1023px) {
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }
}

/* ===== BLOG STYLES ===== */
.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.blog-card {
  background: var(--bg-primary);
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.blog-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.blog-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.blog-card-content {
  padding: 1.5rem;
}

.blog-card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.blog-card-title {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
}

.blog-card-title a {
  color: var(--text-primary);
  text-decoration: none;
}

.blog-card-title a:hover {
  color: var(--gold-primary);
}

.blog-card-excerpt {
  color: var(--text-secondary);
  line-height: 1.6;
}

/* Newsletter */
.newsletter {
  background: var(--bg-accent);
  border-radius: 1rem;
  padding: 3rem;
  text-align: center;
  margin: 4rem 0;
}

.newsletter-form {
  display: flex;
  gap: 1rem;
  max-width: 400px;
  margin: 2rem auto 0;
}

.newsletter-input {
  flex: 1;
  padding: 1rem;
  border: 2px solid var(--border-light);
  border-radius: 0.5rem;
  font-size: 1rem;
}

.newsletter-submit {
  padding: 1rem 2rem;
  background: var(--gradient-gold);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.newsletter-submit:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

@media (max-width: 767px) {
  .newsletter-form {
    flex-direction: column;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .no-print {
    display: none !important;
  }
}
